/**
 * 消息加密解密工具
 * 基于chat-admin.md文档实现的AES加密解密功能
 */

import CryptoJS from 'crypto-js'

// 默认密钥和IV（根据实际项目配置）
const DEFAULT_KEY = '0000000000000000'
const DEFAULT_IV = '0000000000000000'

/**
 * AES加密并转换为Base64
 * @param {string} plaintext - 要加密的明文
 * @param {string} key - 加密密钥，默认使用DEFAULT_KEY
 * @param {string} iv - 初始化向量，默认使用DEFAULT_IV
 * @returns {string} Base64编码的加密结果
 */
export function encryptAESBase64(plaintext, key = DEFAULT_KEY, iv = DEFAULT_IV) {
  try {
    if (!plaintext) {
      console.warn('加密内容为空')
      return ''
    }

    // 将密钥和IV转换为CryptoJS支持的格式
    const cryptoKey = CryptoJS.enc.Utf8.parse(key)
    const cryptoIv = iv ? CryptoJS.enc.Utf8.parse(iv) : null

    // 创建加密配置
    const cfg = {
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
      iv: cryptoIv
    }

    // 执行加密
    const encrypted = CryptoJS.AES.encrypt(plaintext, cryptoKey, cfg)

    // 返回Base64编码的结果
    return encrypted.toString()
  } catch (error) {
    console.error('AES加密失败:', error)
    return null
  }
}

/**
 * 解密Base64编码的AES加密数据
 * @param {string} encryptedBase64 - Base64编码的加密数据
 * @param {string} key - 解密密钥，默认使用DEFAULT_KEY
 * @param {string} iv - 初始化向量，默认使用DEFAULT_IV
 * @returns {string} 解密后的明文
 */
export function decryptAESBase64(encryptedBase64, key = DEFAULT_KEY, iv = DEFAULT_IV) {
  try {
    if (!encryptedBase64) {
      console.warn('解密内容为空')
      return ''
    }

    // 将Base64字符串转换为CryptoJS支持的格式（与chat-app保持一致）
    const encryptedData = CryptoJS.enc.Base64.parse(encryptedBase64)

    // 将密钥和IV转换为CryptoJS支持的格式
    const cryptoKey = CryptoJS.enc.Utf8.parse(key)
    const cryptoIv = iv ? CryptoJS.enc.Utf8.parse(iv) : null

    // 创建解密配置
    const cfg = {
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
      iv: cryptoIv
    }

    // 执行解密（使用与chat-app相同的方式）
    const decrypted = CryptoJS.AES.decrypt(
      { ciphertext: encryptedData },
      cryptoKey,
      cfg
    )

    // 将解密结果转换为UTF-8字符串
    const result = decrypted.toString(CryptoJS.enc.Utf8)

    if (!result) {
      console.warn('解密结果为空，可能密钥不正确或数据损坏')
      return encryptedBase64 // 返回原始数据
    }

    return result
  } catch (error) {
    console.error('AES解密失败:', error)
    return encryptedBase64 // 解密失败时返回原始数据
  }
}

/**
 * 批量加密消息内容
 * @param {Array} messages - 消息数组
 * @param {string} contentField - 内容字段名，默认为'msg'
 * @returns {Array} 加密后的消息数组
 */
export function encryptMessages(messages, contentField = 'msg') {
  if (!Array.isArray(messages)) {
    console.warn('消息数据不是数组格式')
    return messages
  }

  return messages.map(message => {
    if (message[contentField]) {
      return {
        ...message,
        [contentField]: encryptAESBase64(message[contentField])
      }
    }
    return message
  })
}

/**
 * 批量解密消息内容
 * @param {Array} messages - 消息数组
 * @param {string} contentField - 内容字段名，默认为'msg'
 * @returns {Array} 解密后的消息数组
 */
export function decryptMessages(messages, contentField = 'msg') {
  if (!Array.isArray(messages)) {
    console.warn('消息数据不是数组格式')
    return messages
  }

  return messages.map(message => {
    if (message[contentField]) {
      return {
        ...message,
        [contentField]: decryptAESBase64(message[contentField])
      }
    }
    return message
  })
}

/**
 * 检查消息是否需要加密
 * @param {number} typecode2 - 消息类型码
 * @returns {boolean} 是否需要加密
 */
export function shouldEncrypt(typecode2) {
  // 根据文档，typecode2=10的消息不需要加密
  return typecode2 !== 10
}

/**
 * 生成随机密钥
 * @param {number} length - 密钥长度，默认16位
 * @returns {string} 随机密钥
 */
export function generateRandomKey(length = 16) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 生成随机IV
 * @param {number} length - IV长度，默认16位
 * @returns {string} 随机IV
 */
export function generateRandomIV(length = 16) {
  return generateRandomKey(length)
}

/**
 * 验证加密解密功能
 * @param {string} testText - 测试文本
 * @returns {boolean} 验证结果
 */
export function validateEncryption(testText = 'Hello World') {
  try {
    const encrypted = encryptAESBase64(testText)
    const decrypted = decryptAESBase64(encrypted)
    return decrypted === testText
  } catch (error) {
    console.error('加密解密验证失败:', error)
    return false
  }
}

/**
 * 获取加密配置信息
 * @returns {Object} 配置信息
 */
export function getEncryptionConfig() {
  return {
    algorithm: 'AES',
    mode: 'CBC',
    padding: 'Pkcs7',
    keyLength: DEFAULT_KEY.length,
    ivLength: DEFAULT_IV.length,
    encoding: 'Base64'
  }
}

// 在模块加载时验证加密功能
if (process.env.NODE_ENV === 'development') {
  const isValid = validateEncryption()
  if (isValid) {
    console.log('✅ 加密解密功能验证通过')
  } else {
    console.error('❌ 加密解密功能验证失败')
  }
}

export default {
  encryptAESBase64,
  decryptAESBase64,
  encryptMessages,
  decryptMessages,
  shouldEncrypt,
  generateRandomKey,
  generateRandomIV,
  validateEncryption,
  getEncryptionConfig
}
