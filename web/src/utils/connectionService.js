/**
 * WebSocket连接服务
 * 基于chat-admin.md文档实现的即时通讯WebSocket连接管理
 */

import { decryptAESBase64 } from './decrypt'
import { addTabItem, recallMessageInDB } from './db'

class ConnectionService {
  constructor() {
    this.ws = null
    this.isInitialized = false
    this.reconnectTimer = null
    this.heartbeatTimer = null
    this.sendingMessages = new Set() // 发送中的消息集合
    this.connectionTimeout = 20000 // 连接超时时间
    this.reconnectInterval = 5000 // 重连间隔
    this.heartbeatInterval = 30000 // 心跳间隔

    // 回调函数
    this.messageHandler = null // 消息处理回调
    this.connectionHandler = null // 连接状态回调
    this.currentUserId = null // 当前用户ID
  }

  /**
   * 设置消息处理回调
   */
  setMessageHandler(handler) {
    this.messageHandler = handler
  }

  /**
   * 设置连接状态回调
   */
  setConnectionHandler(handler) {
    this.connectionHandler = handler
  }

  /**
   * 设置当前用户ID
   */
  setCurrentUserId(userId) {
    this.currentUserId = userId
  }

  /**
   * 连接WebSocket（简化接口）
   */
  async connect() {
    return new Promise((resolve, reject) => {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token') || 'default_token'

      // 设置临时连接状态回调
      const originalHandler = this.connectionHandler
      this.setConnectionHandler((status) => {
        if (originalHandler) originalHandler(status)

        if (status === 'connected') {
          resolve()
        } else if (status === 'error') {
          reject(new Error('连接失败'))
        }
      })

      this.initWebSocket(token)
    })
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.ws) {
      this.ws.close(1000, '主动断开')
      this.ws = null
    }
    this.isInitialized = false
    this.stopHeartbeat()
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 重新连接
   */
  async reconnect() {
    this.disconnect()
    await this.connect()
  }

  /**
   * 初始化WebSocket连接
   * @param {string} token - 用户认证令牌
   */
  initWebSocket(token) {
    if (this.isInitialized || !token) {
      console.log('WebSocket已初始化或token为空')
      return
    }

    const wsUrl = `ws://43.198.105.182:82/api/msgSocket?token=${token}`
    console.log('正在建立WebSocket连接:', wsUrl)

    try {
      this.ws = new WebSocket(wsUrl)

      // 设置连接超时
      const timeout = setTimeout(() => {
        if (this.ws && this.ws.readyState !== WebSocket.OPEN) {
          console.warn('WebSocket连接超时')
          if (this.connectionHandler) {
            this.connectionHandler('error')
          }
          this.scheduleReconnect(token)
        }
      }, this.connectionTimeout)

      this.ws.onopen = () => {
        clearTimeout(timeout)
        this.isInitialized = true
        console.log('WebSocket连接成功')
        this.startHeartbeat()

        // 清除重连定时器
        if (this.reconnectTimer) {
          clearTimeout(this.reconnectTimer)
          this.reconnectTimer = null
        }

        // 通知连接成功
        if (this.connectionHandler) {
          this.connectionHandler('connected')
        }
      }

      this.ws.onerror = (error) => {
        clearTimeout(timeout)
        console.error('WebSocket错误:', error)
        // 通知连接错误
        if (this.connectionHandler) {
          this.connectionHandler('error')
        }
        this.scheduleReconnect(token)
      }

      this.ws.onclose = (event) => {
        clearTimeout(timeout)
        console.log('WebSocket连接关闭:', event.code, event.reason)
        this.isInitialized = false
        this.stopHeartbeat()

        // 通知连接断开
        if (this.connectionHandler) {
          this.connectionHandler('disconnected')
        }

        // 如果不是主动关闭，则尝试重连
        if (event.code !== 1000) {
          this.scheduleReconnect(token)
        }
      }

      this.ws.onmessage = (event) => {
        this.handleMessage(event)
      }

    } catch (error) {
      console.error('WebSocket连接失败:', error)
      if (this.connectionHandler) {
        this.connectionHandler('error')
      }
      this.scheduleReconnect(token)
    }
  }



  /**
   * 处理接收到的消息
   * @param {MessageEvent} event - WebSocket消息事件
   */
  handleMessage(event) {
    try {
      console.log('收到服务器内容：', event)
      const message = JSON.parse(event.data)
      console.log('解析后的消息:', message)

      // 过滤特定消息类型（与chat-app保持一致）
      if (message.typecode2 == 4 && message.typecode == 3) {
        return
      }

      // 处理语音通话相关消息
      if (message.typecode2 == 11) {
        console.log('发送房间开始通话', message)
        // 这里可以添加语音通话处理逻辑
        return
      }

      // 处理不同类型的消息
      switch (message.typecode2) {
        case 0: // 文本消息
        case 1: // 音频消息
        case 2: // 图片消息
        case 3: // 视频消息
          this.handleChatMessage(message)
          break
        case 5: // 消息撤回
          this.handleRecallMessage(message)
          break
        case 9: // 语音通话
          this.handleVoiceCall(message)
          break
        case 11: // WebRTC信令消息
        case 12:
          this.handleWebRTCSignal(message)
          break
        default:
          console.log('未处理的消息类型:', message.typecode2)
      }
    } catch (error) {
      console.error('处理WebSocket消息失败:', error)
    }
  }

  /**
   * 处理聊天消息
   * @param {Object} message - 消息对象
   */
  async handleChatMessage(message) {
    try {
      // 解密消息内容
      const decryptedMsg = message.typecode2 === 10 ? message.msg : decryptAESBase64(message.msg)

      // 获取当前用户ID
      const currentUserId = this.currentUserId

      // 确定聊天ID
      const chatid = message.typecode === 2 ? message.groupID :
                    (message.toid === currentUserId ? message.fromid : message.toid)

      // 获取用户信息（简化处理）
      let userInfo = {
        head_img: '',
        name: message.fromid,
        avatar: '',
        nickname: message.fromid
      }

      // 构建数据库存储对象
      const dbMessage = {
        id: message.id,
        typecode: message.typecode,
        typecode2: message.typecode2,
        toid: message.toid,
        fromid: message.fromid,
        chatid: chatid,
        t: message.t,
        msg: decryptedMsg,
        isRedRead: 0, // 未读
        idDel: 0, // 未删除
        avatar: userInfo?.head_img || userInfo?.avatar || '',
        nickname: userInfo?.name || userInfo?.nickname || '未知用户',
        senderAvatar: message.typecode === 2 ? userInfo?.head_img || '' : '',
        senderNickname: message.typecode === 2 ? userInfo?.name || '' : '',
        lastMessage: this.getLastMessageText(message.typecode2, decryptedMsg),
        timestamp: new Date(message.t).getTime(),
        unreadCount: 1
      }

      // 存储到本地数据库
      await addTabItem(dbMessage)

      // 通过回调通知外部处理消息
      if (this.messageHandler) {
        this.messageHandler({
          ...message,
          msg: decryptedMsg,
          chatid: chatid,
          userInfo: userInfo
        })
      }

    } catch (error) {
      console.error('处理聊天消息失败:', error)
    }
  }

  /**
   * 处理消息撤回
   * @param {Object} message - 撤回消息对象
   */
  async handleRecallMessage(message) {
    try {
      // 更新数据库中的消息状态
      await recallMessageInDB(message.id)

      // 通过回调通知外部处理消息撤回
      if (this.messageHandler) {
        this.messageHandler({
          ...message,
          typecode2: 5, // 撤回消息标识
          isRecall: true
        })
      }

      console.log('消息撤回成功:', message.id)
    } catch (error) {
      console.error('处理消息撤回失败:', error)
    }
  }

  /**
   * 处理语音通话消息
   * @param {Object} message - 语音通话消息
   */
  handleVoiceCall(message) {
    try {
      // 跳转到语音通话页面
      this.toVoiceCall(message)
    } catch (error) {
      console.error('处理语音通话失败:', error)
    }
  }

  /**
   * 处理WebRTC信令消息
   * @param {Object} message - WebRTC信令消息
   */
  handleWebRTCSignal(message) {
    try {
      // 触发语音通话事件
      if (typeof uni !== 'undefined' && uni.$emit) {
        uni.$emit('chat-voice-received', message)
      } else {
        // 在Web环境中使用事件总线
        window.dispatchEvent(new CustomEvent('chat-voice-received', { detail: message }))
      }
    } catch (error) {
      console.error('处理WebRTC信令失败:', error)
    }
  }

  /**
   * 发送消息
   * @param {Object} messageData - 消息数据
   * @returns {Promise<boolean>} 发送结果
   */
  async sendMessage(messageData) {
    try {
      // 标记消息为发送中
      this.sendingMessages.add(messageData.id)
      
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        // 通过WebSocket发送
        this.ws.send(JSON.stringify(messageData))
        console.log('通过WebSocket发送消息:', messageData)
        return true
      } else {
        // WebSocket不可用，使用HTTP备用接口
        console.warn('WebSocket不可用，使用HTTP备用接口')
        return await this.sendMessageViaHTTP(messageData)
      }
    } catch (error) {
      console.error('发送消息失败:', error)
      return false
    } finally {
      // 移除发送中标记
      this.sendingMessages.delete(messageData.id)
    }
  }

  /**
   * 通过HTTP发送消息（备用方案）
   * @param {Object} messageData - 消息数据
   * @returns {Promise<boolean>} 发送结果
   */
  async sendMessageViaHTTP(messageData) {
    try {
      const response = await fetch('http://43.198.105.182:81/api/chatPush', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(messageData)
      })

      const result = await response.json()
      return result.code === 0
    } catch (error) {
      console.error('HTTP发送消息失败:', error)
      return false
    }
  }

  /**
   * 跳转到语音通话页面
   * @param {Object} message - 语音通话消息
   */
  toVoiceCall(message) {
    const callParams = {
      newMessage: JSON.stringify(message),
      toId: message.fromid,
      isCaller: false,
      id: message.id
    }

    // 在Web环境中，可以通过路由跳转或打开新窗口
    if (typeof window !== 'undefined') {
      // 触发自定义事件，由上层组件处理
      window.dispatchEvent(new CustomEvent('voice-call-request', { detail: callParams }))
    }
  }

  /**
   * 获取用户信息
   * @param {number} userId - 用户ID
   * @returns {Promise<Object>} 用户信息
   */
  async getUserInfo(userId) {
    try {
      const response = await fetch(`http://43.198.105.182:81/api/GetUserByID`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId })
      })

      const result = await response.json()
      if (result.code === 0) {
        return result.data
      }
      return null
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }

  /**
   * 获取最后消息显示文本
   * @param {number} typecode2 - 消息类型
   * @param {string} content - 消息内容
   * @returns {string} 显示文本
   */
  getLastMessageText(typecode2, content) {
    switch (typecode2) {
      case 0:
        return content
      case 1:
        return '[语音消息]'
      case 2:
        return '[图片消息]'
      case 3:
        return '[视频消息]'
      case 5:
        return '[撤回消息]'
      case 9:
        return '[语音通话]'
      default:
        return '[未知消息]'
    }
  }

  /**
   * 开始心跳
   */
  startHeartbeat() {
    this.stopHeartbeat() // 先停止之前的心跳

    this.heartbeatTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: 'heartbeat' }))
      }
    }, this.heartbeatInterval)
  }

  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 安排重连
   * @param {string} token - 用户认证令牌
   */
  scheduleReconnect(token) {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    console.log(`${this.reconnectInterval / 1000}秒后尝试重连...`)
    this.reconnectTimer = setTimeout(() => {
      if (token) {
        console.log('尝试重连WebSocket...')
        // 重置初始化状态，允许重新连接
        this.isInitialized = false
        this.initWebSocket(token)
      }
    }, this.reconnectInterval)
  }

  /**
   * 关闭WebSocket连接
   */
  closeWebSocket() {
    this.isInitialized = false
    this.stopHeartbeat()

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }

    if (this.ws) {
      this.ws.close(1000, '主动关闭')
      this.ws = null
    }
  }

  /**
   * 检查连接状态
   * @returns {boolean} 是否已连接
   */
  isConnected() {
    return this.ws && this.ws.readyState === WebSocket.OPEN
  }

  /**
   * 获取连接状态
   * @returns {string} 连接状态
   */
  getConnectionStatus() {
    if (!this.ws) return 'disconnected'

    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting'
      case WebSocket.OPEN:
        return 'connected'
      case WebSocket.CLOSING:
        return 'closing'
      case WebSocket.CLOSED:
        return 'closed'
      default:
        return 'unknown'
    }
  }
}

// 创建单例实例
const connectionService = new ConnectionService()

export default connectionService
