import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import connectionService from '@/utils/connectionService'
import { addTabItem, recallMessageInDB, getChatList, getMessagesByChatId } from '@/utils/db'
import { decryptAESBase64 } from '@/utils/decrypt'

export const useChatStore = defineStore('chat', () => {
  // 状态
  const chatLists = ref([])
  const messages = ref({}) // 按会话ID存储消息
  const friends = ref([])
  const groups = ref([])
  const onlineUsers = ref([])
  const connectionStatus = ref('disconnected') // disconnected, connecting, connected, error
  const currentUserId = ref('')
  const sendingMessages = ref(new Set()) // 正在发送的消息ID
  const typingUsers = ref({}) // 正在输入的用户

  // Getters
  const getChatMessages = computed(() => (chatId) => {
    return messages.value[chatId] || []
  })

  const getUnreadCount = computed(() => (chatId) => {
    const chat = chatLists.value.find(c => c.id === chatId)
    return chat?.unread || 0
  })

  const isOnline = computed(() => (userId) => {
    return onlineUsers.value.includes(userId)
  })

  const isSending = computed(() => (messageId) => {
    return sendingMessages.value.has(messageId)
  })

  // Actions
  const connect = async () => {
    try {
      connectionStatus.value = 'connecting'

      // 设置当前用户ID
      const currentUserId = localStorage.getItem('userId') || 'default_user'
      connectionService.setCurrentUserId(currentUserId)

      // 设置消息接收回调
      connectionService.setMessageHandler((message) => {
        handleReceivedMessage(message)
      })

      // 设置连接状态回调
      connectionService.setConnectionHandler((status) => {
        connectionStatus.value = status
      })

      // 连接WebSocket
      await connectionService.connect()

      connectionStatus.value = 'connected'
    } catch (error) {
      console.error('连接失败:', error)
      connectionStatus.value = 'error'
      throw error
    }
  }

  const disconnect = async () => {
    try {
      connectionService.disconnect()
      connectionStatus.value = 'disconnected'
    } catch (error) {
      console.error('断开连接失败:', error)
    }
  }

  const reconnect = async () => {
    await disconnect()
    await connect()
  }

  const sendMessage = async (messageData) => {
    try {
      const messageId = messageData.id || Date.now().toString()
      sendingMessages.value.add(messageId)
      
      // 添加到本地消息列表
      addMessage(messageData.chatid || messageData.toid, {
        ...messageData,
        id: messageId,
        sendStatus: 'sending',
        timestamp: new Date().toISOString()
      })
      
      // 发送消息
      const success = await connectionService.sendMessage(messageData)
      
      // 更新消息状态
      updateMessageStatus(messageData.chatid || messageData.toid, messageId, success ? 'sent' : 'failed')
      
      // 存储到本地数据库
      if (success) {
        await addTabItem(messageData)
      }
      
      return success
    } catch (error) {
      console.error('发送消息失败:', error)
      updateMessageStatus(messageData.chatid || messageData.toid, messageData.id, 'failed')
      throw error
    } finally {
      sendingMessages.value.delete(messageData.id)
    }
  }

  const recallMessage = async (messageId, chatId) => {
    try {
      // 构建撤回消息数据
      const recallData = {
        id: Date.now().toString(),
        typecode: 1, // 或根据聊天类型设置
        typecode2: 5, // 撤回消息
        toid: chatId,
        fromid: currentUserId.value,
        msg: messageId, // 要撤回的消息ID
        t: new Date().toISOString()
      }
      
      // 发送撤回请求
      const success = await sendMessage(recallData)
      
      if (success) {
        // 更新本地消息状态
        updateMessageRecall(chatId, messageId)
        
        // 更新数据库
        await recallMessageInDB(messageId)
      }
      
      return success
    } catch (error) {
      console.error('撤回消息失败:', error)
      throw error
    }
  }

  const loadChatLists = async () => {
    try {
      // 从本地数据库加载聊天列表
      const lists = await getChatList()
      chatLists.value = lists || []
    } catch (error) {
      console.error('加载聊天列表失败:', error)
    }
  }

  const loadMessages = async (chatId, page = 1, limit = 20) => {
    try {
      // 从本地数据库加载消息
      const msgs = await getMessagesByChatId(chatId, limit, (page - 1) * limit)
      
      if (!messages.value[chatId]) {
        messages.value[chatId] = []
      }
      
      // 如果是第一页，替换；否则追加到前面
      if (page === 1) {
        messages.value[chatId] = msgs || []
      } else {
        messages.value[chatId] = [...(msgs || []), ...messages.value[chatId]]
      }
      
      return msgs || []
    } catch (error) {
      console.error('加载消息失败:', error)
      return []
    }
  }

  const loadFriends = async () => {
    try {
      // 这里应该从API加载好友列表
      // 暂时使用空数组
      friends.value = []
    } catch (error) {
      console.error('加载好友列表失败:', error)
    }
  }

  const loadGroups = async () => {
    try {
      // 这里应该从API加载群组列表
      // 暂时使用空数组
      groups.value = []
    } catch (error) {
      console.error('加载群组列表失败:', error)
    }
  }

  const loadOnlineUsers = async () => {
    try {
      // 这里应该从API加载在线用户列表
      // 暂时使用空数组
      onlineUsers.value = []
    } catch (error) {
      console.error('加载在线用户失败:', error)
    }
  }

  const addMessage = (chatId, message) => {
    if (!messages.value[chatId]) {
      messages.value[chatId] = []
    }
    messages.value[chatId].push(message)
  }

  const updateMessageStatus = (chatId, messageId, status) => {
    const chatMessages = messages.value[chatId]
    if (chatMessages) {
      const message = chatMessages.find(m => m.id === messageId)
      if (message) {
        message.sendStatus = status
      }
    }
  }

  const updateMessageRecall = (chatId, messageId) => {
    const chatMessages = messages.value[chatId]
    if (chatMessages) {
      const message = chatMessages.find(m => m.id === messageId)
      if (message) {
        message.idDel = 1
        message.msg = '撤回了一条消息'
      }
    }
  }

  const handleReceivedMessage = async (message) => {
    try {
      console.log('处理接收到的消息:', message)

      // 解密消息内容（与chat-app逻辑保持一致）
      let decryptedMsg = ''
      if (message.msg && typeof message.msg === 'string') {
        try {
          // 根据消息类型决定是否解密
          if (message.typecode2 !== 10) { // typecode2=10的消息不需要解密
            decryptedMsg = decryptAESBase64(message.msg)
            message.msg = decryptedMsg
          }
        } catch (error) {
          // 解密失败，可能是明文
          console.warn('消息解密失败，使用原文:', error)
        }
      }

      // 确定聊天ID（根据消息类型）
      const chatId = message.typecode === 1 ? message.fromid : message.toid

      // 添加到消息列表
      addMessage(chatId, {
        ...message,
        timestamp: message.t || new Date().toISOString(),
        sendStatus: 'received'
      })

      // 存储到本地数据库
      await addTabItem(message)

      // 更新聊天列表
      updateChatList(chatId, message)

    } catch (error) {
      console.error('处理接收消息失败:', error)
    }
  }

  const updateChatList = (chatId, message) => {
    let chat = chatLists.value.find(c => c.id === chatId)
    if (!chat) {
      // 创建新的聊天项
      chat = {
        id: chatId,
        typecode: message.typecode,
        name: message.fromid, // 临时使用fromid作为名称
        lastMessage: message.msg,
        lastTime: message.t,
        unread: 1
      }
      chatLists.value.unshift(chat)
    } else {
      // 更新现有聊天项
      chat.lastMessage = message.msg
      chat.lastTime = message.t
      chat.unread = (chat.unread || 0) + 1
      
      // 移到列表顶部
      const index = chatLists.value.indexOf(chat)
      if (index > 0) {
        chatLists.value.splice(index, 1)
        chatLists.value.unshift(chat)
      }
    }
  }

  const markConversationRead = (chatId) => {
    const chat = chatLists.value.find(c => c.id === chatId)
    if (chat) {
      chat.unread = 0
    }
  }

  const setCurrentUser = (userId) => {
    currentUserId.value = userId
  }

  const setTyping = (chatId, userId, isTyping) => {
    if (!typingUsers.value[chatId]) {
      typingUsers.value[chatId] = new Set()
    }
    
    if (isTyping) {
      typingUsers.value[chatId].add(userId)
    } else {
      typingUsers.value[chatId].delete(userId)
    }
  }

  return {
    // 状态
    chatLists,
    messages,
    friends,
    groups,
    onlineUsers,
    connectionStatus,
    currentUserId,
    sendingMessages,
    typingUsers,
    
    // Getters
    getChatMessages,
    getUnreadCount,
    isOnline,
    isSending,
    
    // Actions
    connect,
    disconnect,
    reconnect,
    sendMessage,
    recallMessage,
    loadChatLists,
    loadMessages,
    loadFriends,
    loadGroups,
    loadOnlineUsers,
    addMessage,
    updateMessageStatus,
    updateMessageRecall,
    handleReceivedMessage,
    updateChatList,
    markConversationRead,
    setCurrentUser,
    setTyping
  }
})
