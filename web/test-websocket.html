<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        input[type="text"] {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 300px;
        }
    </style>
</head>
<body>
    <h1>WebSocket连接测试</h1>
    
    <div>
        <label>Token: </label>
        <input type="text" id="tokenInput" placeholder="请输入token" value="default_token">
        <button class="btn-primary" onclick="connect()">连接</button>
        <button class="btn-danger" onclick="disconnect()">断开</button>
    </div>
    
    <div id="status" class="status disconnected">未连接</div>
    
    <div>
        <h3>消息日志:</h3>
        <div id="log" class="log"></div>
        <button class="btn-primary" onclick="clearLog()">清空日志</button>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(status, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = status;
            statusDiv.className = `status ${className}`;
        }

        function connect() {
            const token = document.getElementById('tokenInput').value || 'default_token';
            
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('WebSocket已经连接');
                return;
            }

            const wsUrl = `ws://**************:82/api/msgSocket?token=${token}`;
            log(`正在连接: ${wsUrl}`);
            updateStatus('正在连接...', 'connecting');

            try {
                ws = new WebSocket(wsUrl);

                ws.onopen = function(event) {
                    isConnected = true;
                    log('WebSocket连接成功');
                    updateStatus('已连接', 'connected');
                };

                ws.onmessage = function(event) {
                    log(`收到消息: ${event.data}`);
                    
                    try {
                        const message = JSON.parse(event.data);
                        log(`解析后的消息: ${JSON.stringify(message, null, 2)}`);
                        
                        // 如果消息包含加密内容，尝试解密
                        if (message.msg) {
                            log(`消息内容(加密): ${message.msg}`);
                            // 这里可以添加解密逻辑
                        }
                    } catch (error) {
                        log(`消息解析失败: ${error.message}`);
                    }
                };

                ws.onerror = function(error) {
                    log(`WebSocket错误: ${error}`);
                    updateStatus('连接错误', 'disconnected');
                };

                ws.onclose = function(event) {
                    isConnected = false;
                    log(`WebSocket连接关闭: code=${event.code}, reason=${event.reason}`);
                    updateStatus('连接已断开', 'disconnected');
                };

            } catch (error) {
                log(`连接失败: ${error.message}`);
                updateStatus('连接失败', 'disconnected');
            }
        }

        function disconnect() {
            if (ws) {
                ws.close(1000, '主动断开');
                ws = null;
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 页面加载时自动尝试连接
        window.onload = function() {
            log('页面加载完成，可以开始测试WebSocket连接');
        };
    </script>
</body>
</html>
